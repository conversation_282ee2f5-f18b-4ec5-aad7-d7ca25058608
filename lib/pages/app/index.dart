import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../components/drawer/index.dart';
import '../../components/search/index.dart' as app_search;
import '../../helpers/create_collection_dialog.dart';
import '../../api/api_provider.dart';
import '../../models/collection_item.dart';
import '../../native_bridge/native_bridge.dart';
import '../../constants/app_colors.dart';
import '../../routes.dart';
import '../../services/tag_service.dart';
import 'methods/scroll_listener.dart';
import 'methods/create_collection.dart';
import 'methods/collection_content_builder.dart';
import 'methods/load_favorites.dart';
import 'widgets/permission_panel.dart';
import 'widgets/filter_menu_dialog.dart';
import 'widgets/add_tag_dialog.dart';
import '../../components/collection_grid/index.dart';
import '../../services/version_service.dart';
import '../../widgets/update_install_dialog.dart';
import '../../components/bookmark_grid_view.dart';
import '../../components/bookmark_list_item.dart';
import '../../api/bookmark_api.dart';
import '../../models/tag.dart';
import '../../services/storage_service.dart';
import '../../widgets/custom_toast.dart';



class HomePage extends StatefulWidget {
  const HomePage({super.key, required this.title});

  final String title;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver {
  // 使用GlobalKey来访问Scaffold状态
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  // API提供者
  final _apiProvider = ApiProvider();
  // 版本服务
  final _versionService = VersionService();
  // 标签服务
  final _tagService = TagService();

  // 收藏夹数据列表
  List<CollectionItem> _items = [];

  // 标签映射
  Map<String, Tag> _tagMap = {};

  // 数据加载状态
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 1;

  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  // 过滤状态
  bool _isFilterMode = false; // 是否处于过滤模式
  String? _selectedTag; // 当前选择的标签过滤
  String? _selectedPlatform; // 当前选中的平台
  List<BookmarkItem> _filteredBookmarks = []; // 过滤后的书签列表
  bool _isGridView = false; // 视图模式：false为列表模式，true为大图模式

  // 存储服务
  final _storageService = StorageService();

  // 滚动截流相关变量
  DateTime _lastLoadTime = DateTime.now();

  // 权限面板的key，用于调用刷新方法
  final _permissionPanelKey = GlobalKey<PermissionPanelState>();

  // Native桥接实例
  final _nativeBridge = NativeBridge();

  @override
  Widget build(BuildContext context) {
    // 每次构建时检查视图模式是否需要更新
    _checkAndUpdateViewMode();

    return Scaffold(
        key: _scaffoldKey, // 添加key
        backgroundColor: AppColors.background,
        // 使用AppDrawer组件
        drawer: AppDrawer(),
        // 优化抽屉动画设置
        drawerEdgeDragWidth: 60, // 减小边缘拖动区域，提高响应精准度
        drawerEnableOpenDragGesture: false, // 禁用边缘拖动手势
        drawerScrimColor: Colors.black.withOpacity(0.4), // 使用透明度更高的遮罩颜色，减轻渲染负担

        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        body: Container(
          decoration: BoxDecoration(
            // 添加微妙的渐变背景
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.background,
                Color(0xFFF0F4F9),
              ],
              stops: [0.0, 1.0],
            ),
          ),
          child: SafeArea(
            // 添加NotificationListener监听RefreshCollectionsNotification
            child: NotificationListener<RefreshCollectionsNotification>(
              onNotification: (notification) {
                // 收到刷新通知时，触发刷新
                print('收到刷新收藏夹列表的通知');
                _handleRefresh();
                return true; // 阻止通知继续冒泡
              },
              child: Column(
                children: [
                  SizedBox(height: 8.h),
                  // 使用AppSearchBar组件 - 占满全屏宽度
                  app_search.AppSearchBar(
                    onPersonTap: () {
                      _scaffoldKey.currentState?.openDrawer();
                    },
                    onSearchChanged: (value) {
                      // 处理搜索内容变化
                    },
                    onFilterTap: _showFilterMenu,
                    onCreateTap: () => _showCreateCollectionDialog(context),
                  ),
                  SizedBox(height: 16.h),
                  // 下方内容保持原有的水平内边距
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        children: [
                          // 使用封装的权限检查控制面板
                          PermissionPanel(key: _permissionPanelKey),
                          // 根据过滤模式显示不同内容
                          Expanded(
                            child: _isFilterMode
                              ? _buildFilteredContent()
                              : CollectionContentBuilder.build(
                                  context: context,
                                  isLoading: _isLoading,
                                  isLoadingMore: _isLoadingMore,
                                  hasMoreData: _hasMoreData,
                                  items: _items,
                                  scrollController: _scrollController,
                                  onReorder: _handleReorder,
                                  onCreateCollection: () => _showCreateCollectionDialog(context),
                                  onLoadMore: () => _loadFavorites(loadMore: true),
                                  onRefresh: _handleRefresh,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
    );
  }

  @override
  void initState() {
    super.initState();
    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);
    // 设置状态栏颜色
    _setStatusBarColor();

    // 添加滚动监听
    _scrollController.addListener(_getScrollListener());

    // 加载用户偏好的视图模式
    _loadViewModePreference();

    // 在初始化后安全地加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAndLoadData();
      _loadTagData();
      // 检查权限状态，如果已就绪则自动开启侧边栏
      _checkPermissionsAndAutoEnableSidebar();
    });
  }

  @override
  void dispose() {
    // 释放滚动控制器
    _scrollController.removeListener(_getScrollListener());
    _scrollController.dispose();
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用从后台切换到前台时，刷新权限状态和视图模式
    if (state == AppLifecycleState.resumed) {
      print('应用从后台切换到前台，刷新权限状态和视图模式');

      // 刷新视图模式偏好设置
      _loadViewModePreference();

      // 刷新权限面板状态
      _permissionPanelKey.currentState?.refreshPermissions().then((allEnabled) {
        print('所有权限都已启用: $allEnabled');

        // 如果所有权限都已启用，主动开启侧边栏
        if (allEnabled) {
          _autoEnableSidebarWhenPermissionsReady();
        }
      });
    }
  }

  /// 检查权限状态并在权限就绪时自动开启侧边栏
  Future<void> _checkPermissionsAndAutoEnableSidebar() async {
    try {
      // 延迟一点时间，确保权限面板已经初始化完成
      await Future.delayed(Duration(milliseconds: 500));

      // 检查权限状态
      final accessibilityEnabled = await _nativeBridge.checkAccessibilityPermission();
      final overlayEnabled = await _nativeBridge.checkOverlayPermission();
      final allEnabled = accessibilityEnabled && overlayEnabled;

      print('初始化时权限检查结果 - 无障碍服务: $accessibilityEnabled, 悬浮窗: $overlayEnabled, 全部启用: $allEnabled');

      if (allEnabled) {
        await _autoEnableSidebarWhenPermissionsReady();
      }
    } catch (e) {
      print('检查权限状态时出错: $e');
    }
  }

  /// 当权限准备就绪时自动开启侧边栏
  Future<void> _autoEnableSidebarWhenPermissionsReady() async {
    try {
      print('权限已就绪，准备自动开启侧边栏');

      // 检查侧边栏是否已经在显示
      final isShowing = await _nativeBridge.isSidebarShowing();
      if (isShowing) {
        print('侧边栏已经在显示，无需重复开启');
        return;
      }

      // 显示侧边栏
      final success = await _nativeBridge.showSidebar();
      if (success) {
        print('侧边栏自动开启成功');
      } else {
        print('侧边栏自动开启失败');
      }
    } catch (e) {
      print('自动开启侧边栏时出错: $e');
    }
  }

  // 加载标签数据
  Future<void> _loadTagData() async {
    try {
      final tagObjects = await _tagService.getAllTagObjects();
      if (mounted) {
        setState(() {
          _tagMap = {for (var tag in tagObjects) tag.name: tag};
        });
      }
    } catch (e) {
      print('加载标签数据失败: $e');
    }
  }

  // 加载数据
  Future<void> _initializeAndLoadData() async {
    try {
      // 检查用户是否已登录
      final isLoggedIn = await _apiProvider.isLoggedIn();

      if (!isLoggedIn) {
        // 用户未登录，跳转到微信登录页面
        print('用户未登录，跳转到微信登录页面');
        if (!mounted) return;

        // 使用pushReplacementNamed替换当前路由，防止用户返回到首页
        Navigator.of(context).pushReplacementNamed(AppRoutes.wechatLogin);
        return;
      }

      // 用户已登录，加载收藏夹数据
      print('用户已登录，准备加载收藏夹数据');
      await _loadFavorites();

      // 检查是否有待安装的更新包
      await _checkPendingUpdate();
    } catch (e) {
      print('加载数据过程出错: $e');
    }
  }

  /// 检查待安装的更新包
  Future<void> _checkPendingUpdate() async {
    try {
      // 延迟一点时间，确保页面已经完全加载
      await Future.delayed(Duration(milliseconds: 500));

      final updateInfo = await _versionService.checkPendingUpdateOnAppStart();
      if (updateInfo != null && mounted) {
        // 显示更新安装弹窗
        await UpdateInstallDialog.show(context, updateInfo);
      }
    } catch (e) {
      print('检查待安装更新包失败: $e');
    }
  }

  // 加载用户偏好的视图模式
  Future<void> _loadViewModePreference() async {
    try {
      final isGridView = await _storageService.getGridViewMode();
      setState(() {
        _isGridView = isGridView;
      });
    } catch (e) {
      print('加载视图模式偏好失败: $e');
      // 失败时使用默认值（列表模式）
    }
  }

  // 检查并更新视图模式（用于实时同步）
  void _checkAndUpdateViewMode() {
    _storageService.getGridViewMode().then((isGridView) {
      if (_isGridView != isGridView) {
        setState(() {
          _isGridView = isGridView;
        });
      }
    }).catchError((e) {
      print('检查视图模式失败: $e');
    });
  }

  // 设置状态栏颜色的私有方法
  void _setStatusBarColor() {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // 透明状态栏
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.background, // 导航栏颜色
        systemNavigationBarIconBrightness: Brightness.dark, // 导航栏图标亮度
      ),
    );
  }

  // 处理收藏夹重新排序
  Future<void> _handleReorder(int oldIndex, int newIndex) async {
    print('原始拖动排序: 从 $oldIndex 到 $newIndex');

    // 获取拖动前的项目ID
    final String sourceId = _items[oldIndex].id;

    // 获取目标位置的项目ID
    // 如果向后拖动，需要调整newIndex
    // 注意：ReorderableGridView的索引计算与ReorderableListView不同
    // 这里不需要调整newIndex，因为ReorderableGridView已经处理了这个问题

    // 如果位置相同，不需要更新
    if (oldIndex == newIndex) {
      print('位置相同，不需要更新');
      return;
    }

    final String targetId = _items[newIndex].id;

    print('交换收藏夹: 源ID=$sourceId, 目标ID=$targetId');

    // 先更新UI
    setState(() {
      final element = _items.removeAt(oldIndex);
      _items.insert(newIndex, element);
    });

    try {
      // 调用API交换两个收藏夹的位置
      await _apiProvider.favoritesApi.swapFavorites(
        sourceId: sourceId,
        targetId: targetId,
      );

      print('收藏夹位置交换成功');

      // 刷新列表，确保显示最新的排序
      _handleRefresh();
    } catch (e) {
      print('交换收藏夹位置失败: $e');
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('更新排序失败: ${e.toString()}')),
      );

      // 发生错误时，刷新列表恢复正确顺序
      _handleRefresh();
    }
  }

  // 创建新收藏夹
  Future<void> _handleCreateCollection(String name) async {
    await CreateCollectionUtil.createCollection(
      context: context,
      name: name,
      apiProvider: _apiProvider,
      showLoadingIndicator: _showLoadingIndicator,
      hideLoadingIndicator: _hideLoadingIndicator,
      onSuccess: () {
        // 重新加载收藏夹列表
        _loadFavorites();
      },
    );
  }

  // 显示加载指示器
  void _showLoadingIndicator(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 20),
                Text(message),
              ],
            ),
          ),
        );
      },
    );
  }

  // 隐藏加载指示器
  void _hideLoadingIndicator() {
    Navigator.of(context, rootNavigator: true).pop();
  }

  // 显示过滤菜单
  void _showFilterMenu() {
    FilterMenuDialog.show(
      context,
      onDefaultMode: () {
        setState(() {
          _isFilterMode = false;
          _selectedTag = null;
          _selectedPlatform = null;
          _filteredBookmarks.clear();
        });
        CustomToast.show('已切换到默认模式');
      },
      onTagSelected: (tag) {
        setState(() {
          _selectedTag = tag;
          _selectedPlatform = null;
          _isFilterMode = true;
        });
        _loadFilteredBookmarks();
      },
      onAddTag: () {
        _showAddTagDialog();
      },
      onPlatformSelected: (platform) {
        setState(() {
          _selectedPlatform = platform;
          _selectedTag = null;
          _isFilterMode = true;
        });
        _loadFilteredBookmarks();
      },
    );
  }

  // 显示新增标签对话框
  void _showAddTagDialog() {
    AddTagDialog.show(
      context,
      onConfirm: _addNewTag,
    );
  }

  // 添加新标签
  Future<void> _addNewTag(String tagName) async {
    try {
      final success = await _tagService.addTag(tagName);

      if (success) {
        // 刷新过滤菜单中的标签列表
        FilterMenuDialog.refreshTags();

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('标签"$tagName"添加成功'),
            backgroundColor: AppColors.primary,
            duration: Duration(seconds: 2),
          ),
        );
        print('标签添加成功: $tagName');
      } else {
        // 检查是否是因为标签已存在
        final exists = await _tagService.tagExists(tagName);
        if (exists) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('标签"$tagName"已存在'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('添加标签失败，请重试'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        print('标签添加失败: $tagName');
      }
    } catch (e) {
      print('添加标签时发生错误: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('添加标签时发生错误'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // 显示创建收藏夹对话框
  Future<void> _showCreateCollectionDialog(BuildContext context) async {
    // 检查用户是否已登录
    final isLoggedIn = await _apiProvider.isLoggedIn();

    if (!isLoggedIn) {
      // 用户未登录，跳转到微信登录页面
      if (!mounted) return;

      // 跳转到微信登录页面
      Navigator.of(context).pushNamed(AppRoutes.wechatLogin);
      return;
    }

    // 用户已登录，显示创建收藏夹对话框
    CreateCollectionDialog.show(
      context,
      onConfirm: _handleCreateCollection,
    );
  }



  // 加载收藏夹列表
  Future<void> _loadFavorites({bool loadMore = false}) async {
    // 检查用户是否已登录
    final isLoggedIn = await _apiProvider.isLoggedIn();

    if (!isLoggedIn) {
      // 用户未登录，跳转到微信登录页面
      print('加载收藏夹时检测到用户未登录，跳转到微信登录页面');
      if (!mounted) return;

      // 使用pushReplacementNamed替换当前路由，防止用户返回到首页
      Navigator.of(context).pushReplacementNamed(AppRoutes.wechatLogin);
      return;
    }

    final result = await LoadFavoritesUtil.loadFavorites(
      apiProvider: _apiProvider,
      currentPage: _currentPage,
      isLoading: _isLoading,
      isLoadingMore: _isLoadingMore,
      hasMoreData: _hasMoreData,
      items: _items,
      mounted: mounted,
      loadMore: loadMore,
    );

    if (result.success && mounted) {
      setState(() {
        _items = result.items;
        _currentPage = result.currentPage;
        _hasMoreData = result.hasMoreData;
        _isLoading = result.isLoading;
        _isLoadingMore = result.isLoadingMore;
      });
    } else if (mounted) {
      setState(() {
        _isLoading = result.isLoading;
        _isLoadingMore = result.isLoadingMore;
      });
    }
  }

  // 加载过滤后的书签
  Future<void> _loadFilteredBookmarks({bool loadMore = false}) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      if (!loadMore) {
        _currentPage = 1;
        _filteredBookmarks.clear();
      }
    });

    try {
      // 构建标签过滤参数
      List<String>? tagNames;
      if (_selectedTag != null && _selectedTag!.isNotEmpty) {
        tagNames = [_selectedTag!];
      }

      final result = await _apiProvider.bookmarkApi.getBookmarkList(
        favoriteId: null, // 不传parent_id，在所有收藏夹中过滤
        tagNames: tagNames,
        page: _currentPage,
        pageSize: 20,
        platformType: _selectedPlatform,
      );

      final bookmarks = result['items'] as List<BookmarkItem>? ?? [];
      final total = result['total'] as int? ?? bookmarks.length;

      setState(() {
        if (loadMore) {
          _filteredBookmarks.addAll(bookmarks);
        } else {
          _filteredBookmarks = bookmarks;
        }
        _hasMoreData = _filteredBookmarks.length < total;
        _isLoading = false;
      });

      if (loadMore) {
        _currentPage++;
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('加载过滤书签失败: $e');
    }
  }

  // 处理下拉刷新
  Future<void> _handleRefresh() async {
    print('触发下拉刷新');
    // 重置页码和加载状态
    _currentPage = 1;

    if (_isFilterMode) {
      // 如果是过滤模式，刷新过滤结果
      await _loadFilteredBookmarks();
    } else {
      // 否则刷新收藏夹列表
      await _loadFavorites();
    }
    print('下拉刷新完成');
  }

  // 构建过滤内容
  Widget _buildFilteredContent() {
    // 加载中状态
    if (_isLoading && _filteredBookmarks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 40.r,
              height: 40.r,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                strokeWidth: 3.w,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              '正在加载内容...',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    // 空列表状态
    if (_filteredBookmarks.isEmpty) {
      return Center(
        child: Text(
          '暂无收藏内容',
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.textHint,
            fontWeight: FontWeight.w400,
          ),
        ),
      );
    }

    // 根据视图模式显示不同的组件
    if (_isGridView) {
      // 大图网格模式
      return RefreshIndicator(
        onRefresh: _handleRefresh,
        color: AppColors.primary,
        backgroundColor: Colors.white,
        displacement: 50.0,
        strokeWidth: 3.0,
        child: BookmarkGridView(
          bookmarks: _filteredBookmarks,
          scrollController: _scrollController,
          hasMore: _hasMoreData,
          isLoading: _isLoading,
          onLoadMore: () => _loadFilteredBookmarks(loadMore: true),
          isHomePage: true, // 标记为首页使用
        ),
      );
    } else {
      // 列表模式 - 使用统一的BookmarkListItem组件
      return RefreshIndicator(
        onRefresh: _handleRefresh,
        color: AppColors.primary,
        backgroundColor: Colors.white,
        displacement: 50.0,
        strokeWidth: 3.0,
        child: SlidableAutoCloseBehavior(
          child: ListView.builder(
            controller: _scrollController,
            padding: EdgeInsets.zero,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: _filteredBookmarks.length + (_hasMoreData ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _filteredBookmarks.length) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: SizedBox(
                      width: 24.r,
                      height: 24.r,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                        strokeWidth: 2.w,
                      ),
                    ),
                  ),
                );
              }

              final bookmark = _filteredBookmarks[index];
              return BookmarkListItem(
                bookmark: bookmark,
                tagMap: _tagMap,
                enableSlide: true,
                onRefresh: () => _loadFilteredBookmarks(),
              );
            },
          ),
        ),
      );
    }
  }



  // 获取滚动监听器
  ScrollListener _getScrollListener() {
    return ScrollListenerUtil.create(
      scrollController: _scrollController,
      isLoading: _isLoading,
      isLoadingMore: _isLoadingMore,
      hasMoreData: _hasMoreData,
      lastLoadTime: _lastLoadTime,
      onLoadMore: (DateTime now) {
        // 更新上次加载时间
        _lastLoadTime = now;
        if (_isFilterMode) {
          _loadFilteredBookmarks(loadMore: true);
        } else {
          _loadFavorites(loadMore: true);
        }
      },
    );
  }




}
